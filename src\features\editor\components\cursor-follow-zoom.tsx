import React, { useMemo } from 'react';
import { useCursorFollowZoom } from '../hooks/use-cursor-follow-zoom';
import { useCursorFollowZoomStore } from '../store/use-cursor-follow-zoom-store';
import { CursorOverlayData } from '../../../remotion/CursorOverlay';

/**
 * Props for CursorFollowZoom component
 */
export interface ICursorFollowZoomProps {
  /** Cursor tracking data */
  cursorData: CursorOverlayData | null;
  /** Canvas width in pixels */
  canvasWidth: number;
  /** Canvas height in pixels */
  canvasHeight: number;
  /** Video frames per second */
  fps?: number;
  /** Children to apply zoom transform to */
  children: React.ReactNode;
  /** Whether to override manual zoom effects */
  overrideManualZoom?: boolean;
  /** Additional CSS class name */
  className?: string;
  /** Additional inline styles */
  style?: React.CSSProperties;
}

/**
 * CursorFollowZoom component that applies cursor-following zoom transformations
 * to its children based on cursor position data
 */
export const CursorFollowZoom: React.FC<ICursorFollowZoomProps> = ({
  cursorData,
  canvasWidth,
  canvasHeight,
  fps = 30,
  children,
  overrideManualZoom = false,
  className = '',
  style = {},
}) => {
  const { config } = useCursorFollowZoomStore();
  
  // Get cursor following zoom calculations
  const cursorZoomResult = useCursorFollowZoom(
    cursorData,
    canvasWidth,
    canvasHeight,
    fps
  );

  /**
   * Calculate the transform styles for cursor following zoom
   */
  const transformStyles = useMemo((): React.CSSProperties => {
    // Return no transform if cursor following is not active
    if (!cursorZoomResult.isActive || !config.enabled) {
      return {};
    }

    // Apply zoom transform with smooth transitions
    return {
      transform: `scale(${cursorZoomResult.zoomScale})`,
      transformOrigin: cursorZoomResult.transformOrigin,
      transition: config.smoothing > 0 
        ? `transform ${config.smoothing * 0.3}s ease-out, transform-origin ${config.smoothing * 0.3}s ease-out`
        : 'none',
      // Performance optimizations
      willChange: 'transform',
      backfaceVisibility: 'hidden',
      perspective: 1000,
    };
  }, [
    cursorZoomResult.isActive,
    cursorZoomResult.zoomScale,
    cursorZoomResult.transformOrigin,
    config.enabled,
    config.smoothing,
  ]);

  /**
   * Combine provided styles with transform styles
   */
  const combinedStyles = useMemo((): React.CSSProperties => ({
    ...style,
    ...transformStyles,
  }), [style, transformStyles]);

  /**
   * Determine if cursor following should override manual zoom
   */
  const shouldOverrideManualZoom = useMemo(() => {
    return overrideManualZoom || config.overrideManualZoom;
  }, [overrideManualZoom, config.overrideManualZoom]);

  // If cursor following is not active, render children without transformation
  if (!cursorZoomResult.isActive || !config.enabled) {
    return (
      <div className={className} style={style}>
        {children}
      </div>
    );
  }

  return (
    <div 
      className={`cursor-follow-zoom ${className}`}
      style={combinedStyles}
      data-cursor-follow-active={cursorZoomResult.isActive}
      data-zoom-scale={cursorZoomResult.zoomScale}
      data-override-manual={shouldOverrideManualZoom}
    >
      {children}
    </div>
  );
};

/**
 * Higher-order component that wraps content with cursor following zoom
 */
export const withCursorFollowZoom = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = (props: P & ICursorFollowZoomProps) => {
    const { cursorData, canvasWidth, canvasHeight, fps, overrideManualZoom, ...componentProps } = props;
    
    return (
      <CursorFollowZoom
        cursorData={cursorData}
        canvasWidth={canvasWidth}
        canvasHeight={canvasHeight}
        fps={fps}
        overrideManualZoom={overrideManualZoom}
      >
        <Component {...(componentProps as P)} />
      </CursorFollowZoom>
    );
  };

  WrappedComponent.displayName = `withCursorFollowZoom(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default CursorFollowZoom;
