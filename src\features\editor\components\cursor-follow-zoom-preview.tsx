import React, { useMemo } from 'react';
import { useCursorFollowZoomStore } from '../store/use-cursor-follow-zoom-store';
import { useCursorFollowZoom } from '../hooks/use-cursor-follow-zoom';
import { CursorOverlayData } from '../../../remotion/CursorOverlay';

/**
 * Props for CursorFollowZoomPreview component
 */
export interface ICursorFollowZoomPreviewProps {
  /** Cursor tracking data */
  cursorData: CursorOverlayData | null;
  /** Canvas width in pixels */
  canvasWidth: number;
  /** Canvas height in pixels */
  canvasHeight: number;
  /** Video frames per second */
  fps?: number;
  /** Whether to show the preview overlay */
  show?: boolean;
  /** Additional CSS class name */
  className?: string;
}

/**
 * Visual preview overlay showing the current zoom area and cursor position
 * for debugging and user feedback
 */
export const CursorFollowZoomPreview: React.FC<ICursorFollowZoomPreviewProps> = ({
  cursorData,
  canvasWidth,
  canvasHeight,
  fps = 30,
  show,
  className = '',
}) => {
  const { config } = useCursorFollowZoomStore();
  
  // Get cursor following zoom calculations
  const cursorZoomResult = useCursorFollowZoom(
    cursorData,
    canvasWidth,
    canvasHeight,
    fps
  );

  // Determine if preview should be shown
  const shouldShowPreview = useMemo(() => {
    return (show !== undefined ? show : config.showPreview) && 
           config.enabled && 
           cursorZoomResult.isActive &&
           cursorZoomResult.currentZoomArea;
  }, [show, config.showPreview, config.enabled, cursorZoomResult.isActive, cursorZoomResult.currentZoomArea]);

  // Calculate preview styles
  const previewStyles = useMemo(() => {
    if (!shouldShowPreview || !cursorZoomResult.currentZoomArea) {
      return null;
    }

    const area = cursorZoomResult.currentZoomArea;
    
    return {
      zoomArea: {
        position: 'absolute' as const,
        left: `${area.x * 100}%`,
        top: `${area.y * 100}%`,
        width: `${area.width * 100}%`,
        height: `${area.height * 100}%`,
        border: '2px solid #3b82f6',
        borderRadius: '4px',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        pointerEvents: 'none' as const,
        zIndex: 1000,
        transition: config.smoothing > 0 
          ? `all ${config.smoothing * 0.3}s ease-out`
          : 'none',
      },
      zoomCenter: {
        position: 'absolute' as const,
        left: `${(area.x + area.width / 2) * 100}%`,
        top: `${(area.y + area.height / 2) * 100}%`,
        width: '8px',
        height: '8px',
        backgroundColor: '#3b82f6',
        borderRadius: '50%',
        transform: 'translate(-50%, -50%)',
        pointerEvents: 'none' as const,
        zIndex: 1001,
        transition: config.smoothing > 0 
          ? `all ${config.smoothing * 0.3}s ease-out`
          : 'none',
      },
    };
  }, [shouldShowPreview, cursorZoomResult.currentZoomArea, config.smoothing]);

  // Calculate cursor position styles
  const cursorStyles = useMemo(() => {
    if (!shouldShowPreview || !cursorZoomResult.cursorPosition) {
      return null;
    }

    const cursor = cursorZoomResult.cursorPosition;
    const normalizedX = cursor.x / canvasWidth;
    const normalizedY = cursor.y / canvasHeight;

    return {
      position: 'absolute' as const,
      left: `${normalizedX * 100}%`,
      top: `${normalizedY * 100}%`,
      width: '12px',
      height: '12px',
      backgroundColor: '#ef4444',
      borderRadius: '50%',
      border: '2px solid white',
      transform: 'translate(-50%, -50%)',
      pointerEvents: 'none' as const,
      zIndex: 1002,
      transition: config.smoothing > 0 
        ? `all ${config.smoothing * 0.3}s ease-out`
        : 'none',
    };
  }, [shouldShowPreview, cursorZoomResult.cursorPosition, canvasWidth, canvasHeight, config.smoothing]);

  // Calculate smoothed cursor position styles
  const smoothedCursorStyles = useMemo(() => {
    if (!shouldShowPreview || !cursorZoomResult.smoothedCursorPosition || config.smoothing === 0) {
      return null;
    }

    const smoothed = cursorZoomResult.smoothedCursorPosition;
    const normalizedX = smoothed.x / canvasWidth;
    const normalizedY = smoothed.y / canvasHeight;

    return {
      position: 'absolute' as const,
      left: `${normalizedX * 100}%`,
      top: `${normalizedY * 100}%`,
      width: '8px',
      height: '8px',
      backgroundColor: '#10b981',
      borderRadius: '50%',
      border: '1px solid white',
      transform: 'translate(-50%, -50%)',
      pointerEvents: 'none' as const,
      zIndex: 1003,
      transition: `all ${config.smoothing * 0.3}s ease-out`,
    };
  }, [shouldShowPreview, cursorZoomResult.smoothedCursorPosition, canvasWidth, canvasHeight, config.smoothing]);

  // Don't render if preview should not be shown
  if (!shouldShowPreview) {
    return null;
  }

  return (
    <div 
      className={`cursor-follow-zoom-preview absolute inset-0 pointer-events-none ${className}`}
      style={{ zIndex: 1000 }}
    >
      {/* Zoom Area Rectangle */}
      {previewStyles && (
        <div style={previewStyles.zoomArea}>
          {/* Zoom Area Label */}
          <div
            style={{
              position: 'absolute',
              top: '-24px',
              left: '0',
              backgroundColor: '#3b82f6',
              color: 'white',
              padding: '2px 6px',
              borderRadius: '3px',
              fontSize: '10px',
              fontWeight: 'bold',
              whiteSpace: 'nowrap',
            }}
          >
            Zoom {config.zoomLevel.toFixed(1)}x
          </div>
        </div>
      )}

      {/* Zoom Center Point */}
      {previewStyles && (
        <div style={previewStyles.zoomCenter}>
          <div
            style={{
              position: 'absolute',
              top: '12px',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: '#3b82f6',
              color: 'white',
              padding: '1px 4px',
              borderRadius: '2px',
              fontSize: '8px',
              whiteSpace: 'nowrap',
            }}
          >
            Center
          </div>
        </div>
      )}

      {/* Current Cursor Position */}
      {cursorStyles && (
        <div style={cursorStyles}>
          <div
            style={{
              position: 'absolute',
              top: '16px',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: '#ef4444',
              color: 'white',
              padding: '1px 4px',
              borderRadius: '2px',
              fontSize: '8px',
              whiteSpace: 'nowrap',
            }}
          >
            Cursor
          </div>
        </div>
      )}

      {/* Smoothed Cursor Position */}
      {smoothedCursorStyles && (
        <div style={smoothedCursorStyles}>
          <div
            style={{
              position: 'absolute',
              top: '12px',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: '#10b981',
              color: 'white',
              padding: '1px 4px',
              borderRadius: '2px',
              fontSize: '8px',
              whiteSpace: 'nowrap',
            }}
          >
            Smooth
          </div>
        </div>
      )}

      {/* Debug Info */}
      {shouldShowPreview && (
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '6px 8px',
            borderRadius: '4px',
            fontSize: '10px',
            fontFamily: 'monospace',
            lineHeight: '1.2',
            pointerEvents: 'none',
            zIndex: 1004,
          }}
        >
          <div>Cursor Follow: ON</div>
          <div>Zoom: {config.zoomLevel.toFixed(1)}x</div>
          <div>Area: {(config.zoomAreaSize * 100).toFixed(0)}%</div>
          <div>Smooth: {(config.smoothing * 100).toFixed(0)}%</div>
          {cursorZoomResult.currentZoomArea && (
            <div>
              Pos: {((cursorZoomResult.currentZoomArea.x + cursorZoomResult.currentZoomArea.width / 2) * 100).toFixed(1)}%, {((cursorZoomResult.currentZoomArea.y + cursorZoomResult.currentZoomArea.height / 2) * 100).toFixed(1)}%
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CursorFollowZoomPreview;
