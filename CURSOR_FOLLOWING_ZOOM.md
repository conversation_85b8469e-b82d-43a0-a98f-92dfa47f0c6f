# Cursor Following Zoom Feature

This feature adds automatic zoom functionality that follows the cursor movement in screen recordings, creating dynamic and engaging video content.

## Overview

The cursor following zoom automatically adjusts the video's zoom area to follow the cursor position, creating a smooth tracking effect that keeps the viewer's attention focused on the cursor activity.

## 🎛️ User Interface Location

The cursor following zoom controls are located in the **sidebar under the Zoom Effects section**:

1. **Main Sidebar** → **Zoom Effects** → **Cursor Following Zoom**
2. Look for the section with the 🎯 Target icon
3. The controls include:
   - **Enable/Disable toggle** - Turn cursor following on/off
   - **Zoom Area Size slider** - Control the size of the zoom area (10%-80%)
   - **Smoothing slider** - Adjust transition smoothness (0%-95%)
   - **Movement Threshold slider** - Set minimum cursor movement (0-50px)
   - **Cursor Padding slider** - Control padding around cursor (0%-30%)
   - **Show Preview checkbox** - Toggle visual preview overlay

### Quick Access
- The status indicator shows "Active" or "Inactive" next to the section title
- All settings are immediately applied and saved
- The preview overlay can be toggled to see the zoom area in real-time

## Key Features

- **Automatic Cursor Tracking**: Zoom area automatically follows cursor movement
- **Smooth Transitions**: Configurable smoothing prevents jittery movement
- **Movement Threshold**: Prevents micro-adjustments from constant small movements
- **Customizable Zoom Area**: Adjustable size of the zoom area around the cursor
- **Real-time Preview**: Visual preview of the zoom area during editing
- **Performance Optimized**: Efficient calculations with caching and throttling

## Components

### 1. CursorFollowingZoom Component

The main component that handles cursor following logic:

```tsx
import { CursorFollowingZoom } from './CursorFollowingZoom';

<CursorFollowingZoom
  cursorData={cursorOverlayData}
  enabled={true}
  zoomAreaSize={0.3}        // 30% of canvas
  smoothing={0.7}           // 70% smoothing
  movementThreshold={10}    // 10px minimum movement
  cursorPadding={0.1}       // 10% padding around cursor
  showDebug={false}         // Show debug info
/>
```

### 2. CursorFollowPanel Component

UI panel for configuring cursor following settings:

```tsx
import { CursorFollowPanel } from './cursor-follow-panel';

<CursorFollowPanel />
```

### 3. CursorFollowPreview Component

Visual preview overlay showing the current zoom area:

```tsx
import { CursorFollowPreview } from './cursor-follow-preview';

<CursorFollowPreview
  canvasWidth={800}
  canvasHeight={600}
  show={true}
/>
```

## Configuration Options

### Core Settings

- **enabled**: Enable/disable cursor following zoom
- **zoomAreaSize**: Size of zoom area (0.1 = 10% to 0.8 = 80% of canvas)
- **smoothing**: Smoothing factor (0 = instant, 1 = very smooth)
- **movementThreshold**: Minimum cursor movement in pixels to trigger update
- **cursorPadding**: Padding around cursor within zoom area
- **showPreview**: Show visual preview of zoom area

### Advanced Settings

The cursor following zoom integrates with the existing zoom system, so all standard zoom settings apply:

- **maxZoomScale**: Maximum zoom multiplier
- **bezierControlPoints**: Smooth zoom animation curve
- **zoomOut**: Optional zoom-out phase configuration

## Usage Examples

### Basic Usage

```tsx
// In your video composition
import { CursorFollowingZoom } from './CursorFollowingZoom';

export const MyVideoComposition = () => {
  return (
    <AbsoluteFill>
      {/* Your video content */}
      
      {/* Add cursor following zoom */}
      <CursorFollowingZoom
        cursorData={cursorData}
        enabled={true}
        zoomAreaSize={0.25}
        smoothing={0.8}
      />
      
      {/* Cursor overlay */}
      <CursorOverlay cursorData={cursorData} />
    </AbsoluteFill>
  );
};
```

### With Custom Settings

```tsx
// Custom configuration for different use cases
const tutorialZoomConfig = {
  enabled: true,
  zoomAreaSize: 0.2,      // Smaller area = higher zoom
  smoothing: 0.9,         // Very smooth for tutorials
  movementThreshold: 15,  // Less sensitive to small movements
  cursorPadding: 0.15,    // More padding around cursor
};

<CursorFollowingZoom
  cursorData={cursorData}
  {...tutorialZoomConfig}
/>
```

### Using the Hook

```tsx
import { useCursorFollowingZoom } from './CursorFollowingZoom';

const MyComponent = () => {
  const {
    isEnabled,
    currentZoomArea,
    zoomScale,
    cursorPosition
  } = useCursorFollowingZoom(cursorData, {
    enabled: true,
    zoomAreaSize: 0.3,
    smoothing: 0.7
  });

  return (
    <div>
      <p>Zoom Scale: {zoomScale.toFixed(2)}x</p>
      <p>Cursor: {cursorPosition?.x}, {cursorPosition?.y}</p>
    </div>
  );
};
```

## Integration with Existing Zoom System

The cursor following zoom works seamlessly with the existing zoom effects system:

1. **Zoom Effects Priority**: Manual zoom effects take priority over cursor following
2. **Shared Configuration**: Uses the same zoom store and configuration system
3. **Performance**: Leverages existing zoom calculation optimizations
4. **Compatibility**: Works with all existing zoom features and settings

## Performance Considerations

- **Caching**: Zoom calculations are cached to prevent redundant computations
- **Throttling**: Movement threshold prevents excessive updates
- **Smoothing**: Reduces the frequency of zoom area changes
- **Optimized Rendering**: Preview overlays use efficient CSS transforms

## Best Practices

### For Tutorials and Demos
- Use larger zoom areas (0.3-0.4) for context
- Higher smoothing (0.8-0.9) for professional look
- Higher movement threshold (15-20px) to avoid distractions

### For Gaming or Fast Action
- Smaller zoom areas (0.2-0.3) for focus
- Lower smoothing (0.5-0.7) for responsiveness
- Lower movement threshold (5-10px) for quick tracking

### For Presentations
- Medium zoom areas (0.25-0.35)
- High smoothing (0.8+) for smooth transitions
- Medium threshold (10-15px) for stability

## Troubleshooting

### Common Issues

1. **Zoom area not updating**: Check if cursor following is enabled and cursor data is valid
2. **Jittery movement**: Increase smoothing factor or movement threshold
3. **Zoom too aggressive**: Increase zoom area size or reduce zoom scale
4. **Performance issues**: Check if caching is working and reduce update frequency

### Debug Mode

Enable debug mode to see real-time information:

```tsx
<CursorFollowingZoom
  cursorData={cursorData}
  showDebug={true}
/>
```

This shows:
- Current cursor position
- Zoom area coordinates
- Zoom scale
- Configuration values
- Performance metrics

## API Reference

See the TypeScript interfaces in the source files for complete API documentation:

- `ICursorFollowConfig` - Configuration interface
- `CursorFollowingZoomProps` - Component props
- `useCursorFollowingZoom` - Hook return type

## Future Enhancements

Potential improvements for future versions:

- **Predictive Tracking**: Anticipate cursor movement direction
- **Click-based Zoom**: Zoom in on clicks with different behavior
- **Multi-cursor Support**: Handle multiple cursors in collaborative scenarios
- **Gesture Recognition**: Detect specific cursor patterns for special zoom behaviors
- **Export Presets**: Save and load cursor following configurations
