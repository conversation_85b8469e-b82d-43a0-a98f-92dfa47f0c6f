import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useCurrentFrame } from 'remotion';
import { useCursorFollowZoomStore } from '../store/use-cursor-follow-zoom-store';
import { CursorPoint, CursorOverlayData } from '../../../remotion/CursorOverlay';

/**
 * Cursor following zoom calculation result
 */
export interface ICursorFollowZoomResult {
  /** Whether cursor following zoom is currently active */
  isActive: boolean;
  /** Current zoom area calculated from cursor position */
  currentZoomArea: {
    x: number;
    y: number;
    width: number;
    height: number;
  } | null;
  /** Current zoom scale based on zoom level */
  zoomScale: number;
  /** Current cursor position */
  cursorPosition: CursorPoint | null;
  /** Smoothed cursor position for smooth transitions */
  smoothedCursorPosition: {
    x: number;
    y: number;
  } | null;
  /** Transform origin for CSS transform */
  transformOrigin: string;
}

/**
 * Hook for cursor following zoom functionality
 * Calculates dynamic zoom areas based on cursor position and handles smooth transitions
 */
export const useCursorFollowZoom = (
  cursorData: CursorOverlayData | null,
  canvasWidth: number,
  canvasHeight: number,
  fps: number = 30
): ICursorFollowZoomResult => {
  const frame = useCurrentFrame();
  const { config, state, setCurrentZoomArea, setLastCursorPosition, setIsActive, setSmoothedCursorPosition } = useCursorFollowZoomStore();
  
  // Refs for smooth interpolation and performance optimization
  const lastUpdateTimeRef = useRef<number>(0);
  const animationFrameRef = useRef<number | null>(null);
  const lastFrameRef = useRef<number>(-1);
  const cachedResultRef = useRef<ICursorFollowZoomResult | null>(null);

  /**
   * Get current cursor position from cursor data
   */
  const getCurrentCursorPosition = useCallback((): CursorPoint | null => {
    if (!cursorData || !cursorData.frameData) {
      return null;
    }

    // Find the most recent cursor position at or before current frame
    let currentPosition: CursorPoint | null = null;
    
    for (let f = frame; f >= 0; f--) {
      const framePoints = cursorData.frameData[f];
      if (framePoints && framePoints.length > 0) {
        currentPosition = framePoints[framePoints.length - 1];
        break;
      }
    }

    return currentPosition;
  }, [cursorData, frame]);

  /**
   * Calculate zoom area based on cursor position
   */
  const calculateZoomArea = useCallback((cursorPos: CursorPoint) => {
    if (!cursorPos || canvasWidth === 0 || canvasHeight === 0) {
      return null;
    }

    // Normalize cursor position to 0-1 range
    const normalizedX = cursorPos.x / canvasWidth;
    const normalizedY = cursorPos.y / canvasHeight;

    // Calculate zoom area dimensions
    const areaWidth = config.zoomAreaSize;
    const areaHeight = config.zoomAreaSize;

    // Calculate zoom area position with cursor padding
    const paddingX = areaWidth * config.cursorPadding;
    const paddingY = areaHeight * config.cursorPadding;

    // Center the zoom area around the cursor with padding
    let areaX = normalizedX - areaWidth / 2;
    let areaY = normalizedY - areaHeight / 2;

    // Handle boundary constraints to keep zoom area within canvas
    if (areaX < 0) {
      areaX = 0;
    } else if (areaX + areaWidth > 1) {
      areaX = 1 - areaWidth;
    }

    if (areaY < 0) {
      areaY = 0;
    } else if (areaY + areaHeight > 1) {
      areaY = 1 - areaHeight;
    }

    // Ensure cursor stays within the padded area
    const cursorInAreaX = (normalizedX - areaX) / areaWidth;
    const cursorInAreaY = (normalizedY - areaY) / areaHeight;

    // Adjust area if cursor is too close to edges
    if (cursorInAreaX < config.cursorPadding) {
      areaX = Math.max(0, normalizedX - config.cursorPadding * areaWidth);
    } else if (cursorInAreaX > 1 - config.cursorPadding) {
      areaX = Math.min(1 - areaWidth, normalizedX - (1 - config.cursorPadding) * areaWidth);
    }

    if (cursorInAreaY < config.cursorPadding) {
      areaY = Math.max(0, normalizedY - config.cursorPadding * areaHeight);
    } else if (cursorInAreaY > 1 - config.cursorPadding) {
      areaY = Math.min(1 - areaHeight, normalizedY - (1 - config.cursorPadding) * areaHeight);
    }

    return {
      x: areaX,
      y: areaY,
      width: areaWidth,
      height: areaHeight,
    };
  }, [canvasWidth, canvasHeight, config.zoomAreaSize, config.cursorPadding]);

  /**
   * Apply smoothing to cursor position
   */
  const applySmoothingToCursor = useCallback((
    currentPos: CursorPoint,
    lastSmoothedPos: { x: number; y: number } | null
  ): { x: number; y: number } => {
    if (!lastSmoothedPos || config.smoothing === 0) {
      return { x: currentPos.x, y: currentPos.y };
    }

    const smoothingFactor = config.smoothing;
    return {
      x: lastSmoothedPos.x + (currentPos.x - lastSmoothedPos.x) * (1 - smoothingFactor),
      y: lastSmoothedPos.y + (currentPos.y - lastSmoothedPos.y) * (1 - smoothingFactor),
    };
  }, [config.smoothing]);

  /**
   * Check if cursor has moved enough to trigger an update
   */
  const shouldUpdateZoomArea = useCallback((
    currentPos: CursorPoint,
    lastPos: { x: number; y: number } | null
  ): boolean => {
    if (!lastPos) {
      return true;
    }

    const distance = Math.sqrt(
      Math.pow(currentPos.x - lastPos.x, 2) + Math.pow(currentPos.y - lastPos.y, 2)
    );

    return distance >= config.movementThreshold;
  }, [config.movementThreshold]);

  /**
   * Main cursor following zoom calculation with performance optimizations
   */
  const cursorFollowZoomResult = useMemo((): ICursorFollowZoomResult => {
    // Performance optimization: Return cached result if frame hasn't changed
    if (lastFrameRef.current === frame && cachedResultRef.current) {
      return cachedResultRef.current;
    }

    // Return inactive state if disabled or no cursor data
    if (!config.enabled || !cursorData) {
      const result = {
        isActive: false,
        currentZoomArea: null,
        zoomScale: 1,
        cursorPosition: null,
        smoothedCursorPosition: null,
        transformOrigin: '50% 50%',
      };
      cachedResultRef.current = result;
      lastFrameRef.current = frame;
      return result;
    }

    // Edge case: Invalid canvas dimensions
    if (canvasWidth <= 0 || canvasHeight <= 0) {
      const result = {
        isActive: false,
        currentZoomArea: null,
        zoomScale: 1,
        cursorPosition: null,
        smoothedCursorPosition: null,
        transformOrigin: '50% 50%',
      };
      cachedResultRef.current = result;
      lastFrameRef.current = frame;
      return result;
    }

    const currentCursor = getCurrentCursorPosition();

    if (!currentCursor) {
      const result = {
        isActive: false,
        currentZoomArea: null,
        zoomScale: 1,
        cursorPosition: null,
        smoothedCursorPosition: null,
        transformOrigin: '50% 50%',
      };
      cachedResultRef.current = result;
      lastFrameRef.current = frame;
      return result;
    }

    // Edge case: Cursor position is outside canvas bounds
    if (currentCursor.x < 0 || currentCursor.x > canvasWidth ||
        currentCursor.y < 0 || currentCursor.y > canvasHeight) {
      // Clamp cursor position to canvas bounds
      currentCursor.x = Math.max(0, Math.min(canvasWidth, currentCursor.x));
      currentCursor.y = Math.max(0, Math.min(canvasHeight, currentCursor.y));
    }

    // Check if we should update the zoom area
    const shouldUpdate = shouldUpdateZoomArea(currentCursor, state.lastCursorPosition);
    
    if (!shouldUpdate && state.currentZoomArea) {
      // Return existing zoom area with current cursor
      const centerX = state.currentZoomArea.x + state.currentZoomArea.width / 2;
      const centerY = state.currentZoomArea.y + state.currentZoomArea.height / 2;
      
      return {
        isActive: true,
        currentZoomArea: state.currentZoomArea,
        zoomScale: config.zoomLevel,
        cursorPosition: currentCursor,
        smoothedCursorPosition: state.smoothedCursorPosition,
        transformOrigin: `${centerX * 100}% ${centerY * 100}%`,
      };
    }

    // Apply smoothing to cursor position
    const smoothedCursor = applySmoothingToCursor(currentCursor, state.smoothedCursorPosition);
    
    // Create a smoothed cursor point for zoom area calculation
    const smoothedCursorPoint: CursorPoint = {
      ...currentCursor,
      x: smoothedCursor.x,
      y: smoothedCursor.y,
    };

    // Calculate new zoom area
    const newZoomArea = calculateZoomArea(smoothedCursorPoint);
    
    if (!newZoomArea) {
      return {
        isActive: false,
        currentZoomArea: null,
        zoomScale: 1,
        cursorPosition: currentCursor,
        smoothedCursorPosition: smoothedCursor,
        transformOrigin: '50% 50%',
      };
    }

    // Calculate transform origin from zoom area center
    const centerX = newZoomArea.x + newZoomArea.width / 2;
    const centerY = newZoomArea.y + newZoomArea.height / 2;

    const result = {
      isActive: true,
      currentZoomArea: newZoomArea,
      zoomScale: config.zoomLevel,
      cursorPosition: currentCursor,
      smoothedCursorPosition: smoothedCursor,
      transformOrigin: `${centerX * 100}% ${centerY * 100}%`,
    };

    // Cache the result for performance
    cachedResultRef.current = result;
    lastFrameRef.current = frame;

    return result;
  }, [
    config.enabled,
    config.zoomLevel,
    cursorData,
    getCurrentCursorPosition,
    shouldUpdateZoomArea,
    state.lastCursorPosition,
    state.currentZoomArea,
    state.smoothedCursorPosition,
    applySmoothingToCursor,
    calculateZoomArea,
  ]);

  // Update store state when result changes
  useEffect(() => {
    if (cursorFollowZoomResult.isActive !== state.isActive) {
      setIsActive(cursorFollowZoomResult.isActive);
    }
    
    if (cursorFollowZoomResult.currentZoomArea !== state.currentZoomArea) {
      setCurrentZoomArea(cursorFollowZoomResult.currentZoomArea);
    }
    
    if (cursorFollowZoomResult.cursorPosition && 
        (!state.lastCursorPosition || 
         cursorFollowZoomResult.cursorPosition.x !== state.lastCursorPosition.x ||
         cursorFollowZoomResult.cursorPosition.y !== state.lastCursorPosition.y)) {
      setLastCursorPosition({
        x: cursorFollowZoomResult.cursorPosition.x,
        y: cursorFollowZoomResult.cursorPosition.y,
      });
    }
    
    if (cursorFollowZoomResult.smoothedCursorPosition !== state.smoothedCursorPosition) {
      setSmoothedCursorPosition(cursorFollowZoomResult.smoothedCursorPosition);
    }
  }, [
    cursorFollowZoomResult,
    state,
    setIsActive,
    setCurrentZoomArea,
    setLastCursorPosition,
    setSmoothedCursorPosition,
  ]);

  // Cleanup animation frames on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return cursorFollowZoomResult;
};
