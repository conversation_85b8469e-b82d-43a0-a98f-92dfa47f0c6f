import React, { useState } from 'react';
import { useCursorFollowZoomStore } from '../store/use-cursor-follow-zoom-store';
import { <PERSON><PERSON><PERSON><PERSON>, Zap, Settings, Eye, EyeOff, RotateCcw } from 'lucide-react';

/**
 * Props for CursorFollowZoomPanel component
 */
export interface ICursorFollowZoomPanelProps {
  /** Whether the panel is expanded */
  isExpanded?: boolean;
  /** Callback when panel expansion changes */
  onExpandedChange?: (expanded: boolean) => void;
  /** Additional CSS class name */
  className?: string;
}

/**
 * UI panel for configuring cursor following zoom settings
 */
export const CursorFollowZoomPanel: React.FC<ICursorFollowZoomPanelProps> = ({
  isExpanded = false,
  onExpandedChange,
  className = '',
}) => {
  const {
    config,
    state,
    setEnabled,
    setZoomLevel,
    setZoomAreaSize,
    setSmoothing,
    setMovementThreshold,
    setCursorPadding,
    setShowPreview,
    setOverrideManualZoom,
    resetToDefaults,
  } = useCursorFollowZoomStore();

  const [localExpanded, setLocalExpanded] = useState(isExpanded);

  const handleExpandedChange = (expanded: boolean) => {
    setLocalExpanded(expanded);
    onExpandedChange?.(expanded);
  };

  const expanded = onExpandedChange ? isExpanded : localExpanded;

  return (
    <div className={`cursor-follow-zoom-panel bg-gray-900 border border-gray-700 rounded-lg ${className}`}>
      {/* Header */}
      <div 
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-800 transition-colors"
        onClick={() => handleExpandedChange(!expanded)}
      >
        <div className="flex items-center gap-2">
          <MousePointer size={16} className="text-blue-400" />
          <span className="text-sm font-medium text-white">Cursor Following Zoom</span>
          {state.isActive && (
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          )}
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setEnabled(!config.enabled);
            }}
            className={`px-2 py-1 text-xs rounded transition-colors ${
              config.enabled
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            {config.enabled ? 'ON' : 'OFF'}
          </button>
          <div className={`transform transition-transform ${expanded ? 'rotate-180' : ''}`}>
            ▼
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {expanded && (
        <div className="p-3 border-t border-gray-700 space-y-4">
          {/* Zoom Level */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-sm text-gray-300">
              <Zap size={14} />
              Zoom Level: {config.zoomLevel.toFixed(1)}x
            </label>
            <input
              type="range"
              min="1.0"
              max="5.0"
              step="0.1"
              value={config.zoomLevel}
              onChange={(e) => setZoomLevel(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>1.0x</span>
              <span>3.0x</span>
              <span>5.0x</span>
            </div>
          </div>

          {/* Zoom Area Size */}
          <div className="space-y-2">
            <label className="text-sm text-gray-300">
              Zoom Area Size: {(config.zoomAreaSize * 100).toFixed(0)}%
            </label>
            <input
              type="range"
              min="0.1"
              max="0.8"
              step="0.05"
              value={config.zoomAreaSize}
              onChange={(e) => setZoomAreaSize(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>10%</span>
              <span>45%</span>
              <span>80%</span>
            </div>
          </div>

          {/* Smoothing */}
          <div className="space-y-2">
            <label className="text-sm text-gray-300">
              Smoothing: {(config.smoothing * 100).toFixed(0)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={config.smoothing}
              onChange={(e) => setSmoothing(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Instant</span>
              <span>Smooth</span>
              <span>Very Smooth</span>
            </div>
          </div>

          {/* Movement Threshold */}
          <div className="space-y-2">
            <label className="text-sm text-gray-300">
              Movement Threshold: {config.movementThreshold}px
            </label>
            <input
              type="range"
              min="0"
              max="50"
              step="1"
              value={config.movementThreshold}
              onChange={(e) => setMovementThreshold(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>0px</span>
              <span>25px</span>
              <span>50px</span>
            </div>
          </div>

          {/* Cursor Padding */}
          <div className="space-y-2">
            <label className="text-sm text-gray-300">
              Cursor Padding: {(config.cursorPadding * 100).toFixed(0)}%
            </label>
            <input
              type="range"
              min="0"
              max="0.5"
              step="0.05"
              value={config.cursorPadding}
              onChange={(e) => setCursorPadding(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>0%</span>
              <span>25%</span>
              <span>50%</span>
            </div>
          </div>

          {/* Options */}
          <div className="space-y-3 pt-2 border-t border-gray-700">
            <div className="flex items-center justify-between">
              <label className="flex items-center gap-2 text-sm text-gray-300">
                {config.showPreview ? <Eye size={14} /> : <EyeOff size={14} />}
                Show Preview
              </label>
              <button
                onClick={() => setShowPreview(!config.showPreview)}
                className={`w-10 h-5 rounded-full transition-colors relative ${
                  config.showPreview ? 'bg-blue-600' : 'bg-gray-600'
                }`}
              >
                <div
                  className={`w-4 h-4 bg-white rounded-full absolute top-0.5 transition-transform ${
                    config.showPreview ? 'translate-x-5' : 'translate-x-0.5'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center gap-2 text-sm text-gray-300">
                <Settings size={14} />
                Override Manual Zoom
              </label>
              <button
                onClick={() => setOverrideManualZoom(!config.overrideManualZoom)}
                className={`w-10 h-5 rounded-full transition-colors relative ${
                  config.overrideManualZoom ? 'bg-blue-600' : 'bg-gray-600'
                }`}
              >
                <div
                  className={`w-4 h-4 bg-white rounded-full absolute top-0.5 transition-transform ${
                    config.overrideManualZoom ? 'translate-x-5' : 'translate-x-0.5'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Status and Actions */}
          <div className="pt-2 border-t border-gray-700">
            {state.isActive && (
              <div className="text-xs text-green-400 mb-2">
                ✓ Cursor following active
                {state.currentZoomArea && (
                  <div className="text-gray-400 mt-1">
                    Area: {(state.currentZoomArea.width * 100).toFixed(1)}% × {(state.currentZoomArea.height * 100).toFixed(1)}%
                  </div>
                )}
              </div>
            )}
            
            <button
              onClick={resetToDefaults}
              className="flex items-center gap-2 px-3 py-1.5 text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
            >
              <RotateCcw size={12} />
              Reset to Defaults
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CursorFollowZoomPanel;
