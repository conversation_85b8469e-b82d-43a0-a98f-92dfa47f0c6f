import { create } from "zustand";

/**
 * Cursor following zoom configuration interface
 */
export interface ICursorFollowZoomConfig {
  /** Whether cursor following zoom is enabled */
  enabled: boolean;
  /** Zoom level multiplier (1.5x, 2x, 3x, etc.) */
  zoomLevel: number;
  /** Size of the zoom area as a percentage of canvas (0.1 = 10% to 0.8 = 80%) */
  zoomAreaSize: number;
  /** Smoothing factor for cursor movement (0 = instant, 1 = very smooth) */
  smoothing: number;
  /** Minimum cursor movement in pixels to trigger zoom area update */
  movementThreshold: number;
  /** Padding around cursor within zoom area (0.1 = 10% padding) */
  cursorPadding: number;
  /** Whether to show visual preview of zoom area */
  showPreview: boolean;
  /** Maximum zoom scale to prevent excessive zooming */
  maxZoomScale: number;
  /** Minimum zoom area size to prevent too small areas */
  minZoomAreaSize: number;
  /** Whether to prioritize cursor following over manual zoom effects */
  overrideManualZoom: boolean;
}

/**
 * Cursor following zoom state interface
 */
export interface ICursorFollowZoomState {
  /** Current zoom area being followed */
  currentZoomArea: {
    x: number;
    y: number;
    width: number;
    height: number;
  } | null;
  /** Last cursor position that triggered an update */
  lastCursorPosition: {
    x: number;
    y: number;
  } | null;
  /** Whether cursor following is currently active */
  isActive: boolean;
  /** Smoothed cursor position for smooth transitions */
  smoothedCursorPosition: {
    x: number;
    y: number;
  } | null;
}

/**
 * Cursor following zoom store interface
 */
export interface ICursorFollowZoomStore {
  config: ICursorFollowZoomConfig;
  state: ICursorFollowZoomState;
  
  // Configuration setters
  setEnabled: (enabled: boolean) => void;
  setZoomLevel: (level: number) => void;
  setZoomAreaSize: (size: number) => void;
  setSmoothing: (smoothing: number) => void;
  setMovementThreshold: (threshold: number) => void;
  setCursorPadding: (padding: number) => void;
  setShowPreview: (show: boolean) => void;
  setOverrideManualZoom: (override: boolean) => void;
  
  // State setters
  setCurrentZoomArea: (area: ICursorFollowZoomState['currentZoomArea']) => void;
  setLastCursorPosition: (position: ICursorFollowZoomState['lastCursorPosition']) => void;
  setIsActive: (active: boolean) => void;
  setSmoothedCursorPosition: (position: ICursorFollowZoomState['smoothedCursorPosition']) => void;
  
  // Utility methods
  resetToDefaults: () => void;
  resetState: () => void;
}

/**
 * Default cursor following zoom configuration
 */
export const DEFAULT_CURSOR_FOLLOW_ZOOM_CONFIG: ICursorFollowZoomConfig = {
  enabled: false,
  zoomLevel: 2.0,
  zoomAreaSize: 0.3, // 30% of canvas
  smoothing: 0.7, // 70% smoothing for smooth movement
  movementThreshold: 10, // 10px minimum movement
  cursorPadding: 0.1, // 10% padding around cursor
  showPreview: false,
  maxZoomScale: 5.0, // Maximum 5x zoom
  minZoomAreaSize: 0.1, // Minimum 10% area size
  overrideManualZoom: false, // Don't override manual zoom by default
};

/**
 * Default cursor following zoom state
 */
export const DEFAULT_CURSOR_FOLLOW_ZOOM_STATE: ICursorFollowZoomState = {
  currentZoomArea: null,
  lastCursorPosition: null,
  isActive: false,
  smoothedCursorPosition: null,
};

/**
 * Cursor following zoom store implementation
 */
export const useCursorFollowZoomStore = create<ICursorFollowZoomStore>((set) => ({
  config: DEFAULT_CURSOR_FOLLOW_ZOOM_CONFIG,
  state: DEFAULT_CURSOR_FOLLOW_ZOOM_STATE,

  // Configuration setters
  setEnabled: (enabled) =>
    set((state) => ({
      config: { ...state.config, enabled },
      // Reset state when disabling
      state: enabled ? state.state : DEFAULT_CURSOR_FOLLOW_ZOOM_STATE,
    })),

  setZoomLevel: (level) =>
    set((state) => ({
      config: { ...state.config, zoomLevel: Math.max(1.0, Math.min(state.config.maxZoomScale, Number(level) || 1.0)) }
    })),

  setZoomAreaSize: (size) =>
    set((state) => ({
      config: { ...state.config, zoomAreaSize: Math.max(state.config.minZoomAreaSize, Math.min(0.8, Number(size) || 0.3)) }
    })),

  setSmoothing: (smoothing) =>
    set((state) => ({
      config: { ...state.config, smoothing: Math.max(0, Math.min(1, Number(smoothing) || 0)) }
    })),

  setMovementThreshold: (threshold) =>
    set((state) => ({
      config: { ...state.config, movementThreshold: Math.max(0, Number(threshold) || 0) }
    })),

  setCursorPadding: (padding) =>
    set((state) => ({
      config: { ...state.config, cursorPadding: Math.max(0, Math.min(0.5, padding)) }
    })),

  setShowPreview: (show) =>
    set((state) => ({
      config: { ...state.config, showPreview: show }
    })),

  setOverrideManualZoom: (override) =>
    set((state) => ({
      config: { ...state.config, overrideManualZoom: override }
    })),

  // State setters
  setCurrentZoomArea: (area) =>
    set((state) => ({
      state: { ...state.state, currentZoomArea: area }
    })),

  setLastCursorPosition: (position) =>
    set((state) => ({
      state: { ...state.state, lastCursorPosition: position }
    })),

  setIsActive: (active) =>
    set((state) => ({
      state: { ...state.state, isActive: active }
    })),

  setSmoothedCursorPosition: (position) =>
    set((state) => ({
      state: { ...state.state, smoothedCursorPosition: position }
    })),

  // Utility methods
  resetToDefaults: () =>
    set(() => ({
      config: DEFAULT_CURSOR_FOLLOW_ZOOM_CONFIG,
      state: DEFAULT_CURSOR_FOLLOW_ZOOM_STATE,
    })),

  resetState: () =>
    set((state) => ({
      state: DEFAULT_CURSOR_FOLLOW_ZOOM_STATE,
    })),
}));
